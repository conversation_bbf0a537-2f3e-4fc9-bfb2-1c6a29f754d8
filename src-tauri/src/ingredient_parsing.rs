use anyhow::Result;
use serde::{Deserialize, Serialize};
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::sync::{Mute<PERSON>, Semaphore};
use tracing::{debug, info, warn, error};
use kalosm::language::*;

/// Kalosm-compatible ingredient structure for structured generation
#[derive(<PERSON><PERSON>, Debug, Serialize, Deserialize)]
pub struct KalosmIngredient {
    /// The ingredient name (e.g., "all-purpose flour", "chicken breast")
    pub name: String,

    /// The amount as a decimal number (e.g., 2.5, 0.25, 1.0)
    pub amount: f64,

    /// The unit of measurement (e.g., "cup", "tbsp", "lb", "oz", "unit", "")
    pub unit: String,

    /// Optional section for grouped ingredients (e.g., "Cake Layer", "Frosting")
    pub section: Option<String>,
}

/// Performance metrics for monitoring parsing operations
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct ParsingMetrics {
    pub total_requests: u64,
    pub successful_parses: u64,
    pub fallback_uses: u64,
    pub average_latency_ms: f64,
    pub model_load_time_ms: Option<u64>,
}

impl Default for ParsingMetrics {
    fn default() -> Self {
        Self {
            total_requests: 0,
            successful_parses: 0,
            fallback_uses: 0,
            average_latency_ms: 0.0,
            model_load_time_ms: None,
        }
    }
}

/// Ingredient parsing service using Kalosm for local AI inference
pub struct IngredientParser {
    /// The Kalosm language model for structured generation
    model: Arc<Mutex<Option<Llama>>>,
    /// Semaphore to limit concurrent parsing operations
    parsing_semaphore: Arc<Semaphore>,
    /// Performance metrics
    metrics: Arc<Mutex<ParsingMetrics>>,
    /// Model warming flag
    model_warmed: Arc<Mutex<bool>>,
}

impl IngredientParser {
    /// Create a new ingredient parser instance
    pub fn new() -> Self {
        // Log hardware acceleration capabilities
        crate::kalosm_config::KalosmConfig::log_hardware_acceleration_info();

        Self {
            model: Arc::new(Mutex::new(None)),
            // Limit concurrent parsing to 3 operations to manage memory usage
            parsing_semaphore: Arc::new(Semaphore::new(3)),
            metrics: Arc::new(Mutex::new(ParsingMetrics::default())),
            model_warmed: Arc::new(Mutex::new(false)),
        }
    }

    /// Get current performance metrics
    pub async fn get_metrics(&self) -> ParsingMetrics {
        self.metrics.lock().await.clone()
    }

    /// Warm up the model by loading it and running a test inference
    pub async fn warm_model(&self) -> Result<()> {
        let mut warmed = self.model_warmed.lock().await;
        if *warmed {
            return Ok(());
        }

        info!("Warming up Kalosm model with hardware acceleration...");
        let start_time = Instant::now();

        // Load the model
        self.ensure_model_loaded().await?;

        // Run a simple test inference to warm up the model
        let test_result = self.kalosm_parse("1 cup flour", None).await;
        match test_result {
            Ok(_) => {
                let warm_time = start_time.elapsed();
                info!("Model warmed up successfully in {}ms with optimizations enabled", warm_time.as_millis());

                // Update metrics
                let mut metrics = self.metrics.lock().await;
                metrics.model_load_time_ms = Some(warm_time.as_millis() as u64);

                *warmed = true;
                Ok(())
            }
            Err(e) => {
                warn!("Model warming failed: {}", e);
                Err(e)
            }
        }
    }

    /// Initialize the Kalosm model (lazy loading) with optimized settings
    async fn ensure_model_loaded(&self) -> Result<()> {
        let mut model_guard = self.model.lock().await;

        if model_guard.is_none() {
            info!("Loading Kalosm Phi-3 model for ingredient parsing with hardware acceleration...");

            // Use Phi-3 model which is lightweight and good for structured generation
            // This is a good balance between size (~2.4GB) and performance
            // The model will automatically use hardware acceleration (Metal/CUDA/MKL) if available
            match Llama::phi_3().await {
                Ok(model) => {
                    info!("Successfully loaded Kalosm Phi-3 model with optimizations");
                    *model_guard = Some(model);
                }
                Err(e) => {
                    error!("Failed to load Kalosm model: {}", e);
                    return Err(e.into());
                }
            }
        }

        Ok(())
    }

    /// Parse a single ingredient string using Kalosm structured generation
    pub async fn parse_ingredient(
        &self,
        ingredient_text: &str,
        section: Option<String>,
    ) -> Result<Option<crate::database::Ingredient>> {
        // Skip empty or invalid ingredients
        if ingredient_text.trim().is_empty() {
            return Ok(None);
        }

        // Acquire semaphore permit to limit concurrent operations
        let _permit = self.parsing_semaphore.acquire().await.unwrap();

        let start_time = Instant::now();
        debug!("Parsing ingredient with Kalosm: '{}'", ingredient_text);

        // Update metrics
        {
            let mut metrics = self.metrics.lock().await;
            metrics.total_requests += 1;
        }

        // Try Kalosm parsing first
        let result = match self.kalosm_parse(ingredient_text, section.clone()).await {
            Ok(Some(ingredient)) => {
                debug!("Successfully parsed with Kalosm: {:?}", ingredient);

                // Update success metrics
                {
                    let mut metrics = self.metrics.lock().await;
                    metrics.successful_parses += 1;
                }

                Ok(Some(ingredient))
            }
            Ok(None) => {
                debug!("Kalosm returned None for ingredient: '{}'", ingredient_text);
                Ok(None)
            }
            Err(e) => {
                warn!("Kalosm parsing failed for '{}': {}, using fallback", ingredient_text, e);

                // Update fallback metrics
                {
                    let mut metrics = self.metrics.lock().await;
                    metrics.fallback_uses += 1;
                }

                // Fallback to regex parsing
                self.fallback_parse(ingredient_text, section)
            }
        };

        // Update latency metrics
        let elapsed = start_time.elapsed();
        {
            let mut metrics = self.metrics.lock().await;
            let total_time = metrics.average_latency_ms * (metrics.total_requests - 1) as f64;
            metrics.average_latency_ms = (total_time + elapsed.as_millis() as f64) / metrics.total_requests as f64;
        }

        result
    }

    /// Parse ingredient using Kalosm text generation
    async fn kalosm_parse(
        &self,
        ingredient_text: &str,
        section: Option<String>,
    ) -> Result<Option<crate::database::Ingredient>> {
        // Ensure model is loaded
        self.ensure_model_loaded().await?;

        let model_guard = self.model.lock().await;
        let model = model_guard.as_ref().ok_or_else(|| {
            anyhow::anyhow!("Model not loaded")
        })?;

        // Create a prompt for ingredient parsing
        let prompt = format!(
            "Parse the following ingredient into JSON format with fields: name (string), amount (number), unit (string). If no amount is specified, use 1.0. If no unit is specified, use 'unit'. Return only valid JSON without any explanation.\n\nIngredient: \"{}\"\n\nJSON:",
            ingredient_text
        );

        debug!("Kalosm prompt: {}", prompt);

        // Use task-based generation for better consistency
        let task = model.task("You are an expert ingredient parser. Parse ingredients into JSON format with name, amount, and unit fields. Return only valid JSON.");

        match task(&prompt).await {
            Ok(response) => {
                debug!("Kalosm response: {}", response);

                // Try to parse the JSON response
                match self.parse_kalosm_response(&response, section) {
                    Ok(Some(ingredient)) => Ok(Some(ingredient)),
                    Ok(None) => {
                        debug!("Failed to parse Kalosm JSON response");
                        Err(anyhow::anyhow!("Invalid JSON response from Kalosm"))
                    }
                    Err(e) => {
                        debug!("Error parsing Kalosm response: {}", e);
                        Err(e)
                    }
                }
            }
            Err(e) => {
                debug!("Kalosm text generation failed: {}", e);
                Err(e.into())
            }
        }
    }

    /// Parse the JSON response from Kalosm
    fn parse_kalosm_response(
        &self,
        response: &str,
        section: Option<String>,
    ) -> Result<Option<crate::database::Ingredient>> {
        // Clean up the response - remove any markdown formatting or extra text
        let json_str = response
            .trim()
            .trim_start_matches("```json")
            .trim_start_matches("```")
            .trim_end_matches("```")
            .trim();

        // Try to parse as JSON
        match serde_json::from_str::<KalosmIngredient>(json_str) {
            Ok(kalosm_ingredient) => {
                // Convert to database ingredient
                let db_ingredient = crate::database::Ingredient {
                    name: kalosm_ingredient.name.trim().to_string(),
                    amount: kalosm_ingredient.amount.to_string(),
                    unit: normalize_unit(&kalosm_ingredient.unit),
                    category: None,
                    section: section.or(kalosm_ingredient.section),
                };

                // Validate the parsed ingredient
                if db_ingredient.name.is_empty() {
                    debug!("Kalosm returned empty ingredient name");
                    return Ok(None);
                }

                Ok(Some(db_ingredient))
            }
            Err(e) => {
                debug!("Failed to parse JSON response: {} - Response: {}", e, json_str);
                Err(anyhow::anyhow!("Invalid JSON format: {}", e))
            }
        }
    }

    /// Fallback to existing regex-based parsing
    fn fallback_parse(
        &self,
        ingredient_text: &str,
        section: Option<String>,
    ) -> Result<Option<crate::database::Ingredient>> {
        // Simple parsing logic for now - can be enhanced later
        let trimmed = ingredient_text.trim();

        if trimmed.is_empty() {
            return Ok(None);
        }

        // Basic regex pattern to extract amount, unit, and name
        let pattern = r"^(\d+(?:\.\d+)?(?:\s*[¼½¾⅓⅔⅛⅜⅝⅞])?)\s*([a-zA-Z]+)?\s*(.+)$";

        if let Ok(regex) = regex::Regex::new(pattern) {
            if let Some(captures) = regex.captures(trimmed) {
                let amount_str = captures.get(1).map(|m| m.as_str()).unwrap_or("1");
                let unit = captures.get(2).map(|m| m.as_str()).unwrap_or("unit");
                let name = captures.get(3).map(|m| m.as_str()).unwrap_or(trimmed);

                let amount = amount_str.parse::<f64>().unwrap_or(1.0);

                return Ok(Some(crate::database::Ingredient {
                    name: name.trim().to_string(),
                    amount: amount.to_string(),
                    unit: normalize_unit(unit),
                    category: None,
                    section,
                }));
            }
        }

        // Fallback: treat as ingredient name with amount 1
        Ok(Some(crate::database::Ingredient {
            name: trimmed.to_string(),
            amount: "1".to_string(),
            unit: "unit".to_string(),
            category: None,
            section,
        }))
    }

    /// Parse multiple ingredients in batch with optimized parallel processing
    pub async fn parse_ingredients_batch(
        &self,
        ingredients: &[String],
    ) -> Result<Vec<crate::database::Ingredient>> {
        if ingredients.is_empty() {
            return Ok(Vec::new());
        }

        info!("Starting batch parsing of {} ingredients", ingredients.len());
        let start_time = Instant::now();

        // For now, process sequentially to avoid lifetime issues
        // TODO: Implement proper parallel processing with owned data
        let mut parsed_ingredients = Vec::new();

        for ingredient_str in ingredients {
            // Check if ingredient has section information (format: [Section Name] ingredient text)
            let (section, ingredient_text) = if let Some(captures) =
                regex::Regex::new(r"^\[([^\]]+)\]\s*(.+)$")
                    .unwrap()
                    .captures(ingredient_str)
            {
                (Some(captures[1].to_string()), captures[2].to_string())
            } else {
                (None, ingredient_str.clone())
            };

            if let Ok(Some(ingredient)) = self.parse_ingredient(&ingredient_text, section).await {
                parsed_ingredients.push(ingredient);
            }
        }

        let elapsed = start_time.elapsed();
        info!(
            "Batch parsing completed: {}/{} ingredients parsed in {}ms (avg: {}ms per ingredient)",
            parsed_ingredients.len(),
            ingredients.len(),
            elapsed.as_millis(),
            elapsed.as_millis() as f64 / ingredients.len() as f64
        );

        Ok(parsed_ingredients)
    }

    /// Parse ingredients in optimized batches for better memory management
    pub async fn parse_ingredients_chunked(
        &self,
        ingredients: &[String],
        chunk_size: usize,
    ) -> Result<Vec<crate::database::Ingredient>> {
        let mut all_parsed = Vec::new();

        for chunk in ingredients.chunks(chunk_size) {
            let chunk_results = self.parse_ingredients_batch(chunk).await?;
            all_parsed.extend(chunk_results);

            // Small delay between chunks to prevent overwhelming the system
            tokio::time::sleep(Duration::from_millis(10)).await;
        }

        Ok(all_parsed)
    }
}

/// Normalize unit names to standard abbreviations
fn normalize_unit(unit: &str) -> String {
    match unit.to_lowercase().as_str() {
        "cup" | "cups" => "cup".to_string(),
        "tablespoon" | "tablespoons" | "tbsp" => "tbsp".to_string(),
        "teaspoon" | "teaspoons" | "tsp" => "tsp".to_string(),
        "pound" | "pounds" | "lb" => "lb".to_string(),
        "ounce" | "ounces" | "oz" => "oz".to_string(),
        "gram" | "grams" | "g" => "g".to_string(),
        "kilogram" | "kilograms" | "kg" => "kg".to_string(),
        "milliliter" | "milliliters" | "ml" => "ml".to_string(),
        "liter" | "liters" | "l" => "l".to_string(),
        "can" | "cans" => "can".to_string(),
        "package" | "packages" => "package".to_string(),
        "jar" | "jars" => "jar".to_string(),
        "bottle" | "bottles" => "bottle".to_string(),
        "bag" | "bags" => "bag".to_string(),
        "box" | "boxes" => "box".to_string(),
        "piece" | "pieces" => "piece".to_string(),
        "slice" | "slices" => "slice".to_string(),
        "clove" | "cloves" => "clove".to_string(),
        "stalk" | "stalks" => "stalk".to_string(),
        "" => "".to_string(),
        _ => "unit".to_string(),
    }
}

/// Global instance of the ingredient parser
static INGREDIENT_PARSER: std::sync::OnceLock<IngredientParser> = std::sync::OnceLock::new();

/// Get the global ingredient parser instance
pub fn get_ingredient_parser() -> &'static IngredientParser {
    INGREDIENT_PARSER.get_or_init(|| IngredientParser::new())
}

#[cfg(test)]
mod tests;

#[cfg(test)]
mod performance_tests;

#[cfg(test)]
mod integration_tests;
